#!/bin/bash

# ==============================================================================
# SSH Tunnel Manager (v2 - Color Fix)
#
# Manages multiple SSH tunnels based on a JSON configuration file, leveraging
# ~/.ssh/config for connection details.
# ==============================================================================

# --- Configuration ---
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/config.json"
PID_DIR="$SCRIPT_DIR/tmp/pids"

# --- Colors ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# --- Helper Functions ---
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_debug() { echo -e "${BLUE}[DEBUG]${NC} $1"; }

# --- Core Functions ---

# 检查依赖项 (jq) 和文件
check_dependencies() {
    if ! command -v jq &> /dev/null; then
        log_error "依赖 'jq' 未安装. 请先安装 (e.g., 'brew install jq')."
        exit 1
    fi
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    mkdir -p "$PID_DIR"
}

# 检查隧道是否正在运行
is_tunnel_running() {
    local tunnel_name="$1"
    local pid_file="$PID_DIR/$tunnel_name.pid"
    if [ -f "$pid_file" ]; then
        local pid
        pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0 # 正在运行
        else
            rm "$pid_file" # 清理无效的PID文件
            return 1
        fi
    fi
    return 1
}

# 启动单个隧道
start_tunnel() {
    local tunnel_name="$1"
    local config
    config=$(jq -r ".tunnels.\"$tunnel_name\"" "$CONFIG_FILE")

    if [ "$config" == "null" ]; then
        log_error "隧道 '$tunnel_name' 在配置文件中未定义."
        return 1
    fi

    if is_tunnel_running "$tunnel_name"; then
        log_warn "隧道 '$tunnel_name' 已在运行."
        return 0
    fi

    # 解析配置
    local name local_port remote_host remote_port ssh_host
    name=$(echo "$config" | jq -r '.name')
    local_port=$(echo "$config" | jq -r '.local_port')
    remote_host=$(echo "$config" | jq -r '.remote_host')
    remote_port=$(echo "$config" | jq -r '.remote_port')
    ssh_host=$(echo "$config" | jq -r '.ssh_host')

    # 验证 SSH 主机配置
    if ! ssh -G "$ssh_host" &>/dev/null; then
        log_error "SSH 主机 '$ssh_host' 在 ~/.ssh/config 中未找到或配置错误."
        return 1
    fi

    # 检查本地端口是否被占用
    if nc -z localhost "$local_port" 2>/dev/null; then
        log_error "本地端口 $local_port 已被占用, 无法启动隧道 '$name'."
        return 1
    fi

    log_info "正在启动隧道: $name ($tunnel_name)..."
    log_debug "命令: ssh -N -f -L ${local_port}:${remote_host}:${remote_port} ${ssh_host}"

    ssh -N -f -L "${local_port}:${remote_host}:${remote_port}" "${ssh_host}"
    local ssh_exit_code=$?

    if [ $ssh_exit_code -ne 0 ]; then
        log_error "SSH 命令执行失败 (退出码: $ssh_exit_code). 请检查 ~/.ssh/config 和网络连接."
        return 1
    fi

    sleep 2
    local pid
    pid=$(ps aux | grep "[s]sh -N -f -L ${local_port}:${remote_host}:${remote_port} ${ssh_host}" | awk '{print $2}' | head -1)

    if [ -z "$pid" ]; then
        log_error "无法获取隧道进程的 PID. 启动可能失败."
        return 1
    fi

    echo "$pid" > "$PID_DIR/$tunnel_name.pid"
    log_info "✓ 隧道 '$name' 启动成功. (PID: $pid, 端口: $local_port)"
}

# 停止单个隧道
stop_tunnel() {
    local tunnel_name="$1"
    local pid_file="$PID_DIR/$tunnel_name.pid"

    if ! is_tunnel_running "$tunnel_name"; then
        log_warn "隧道 '$tunnel_name' 未运行."
        return 0
    fi

    local pid
    pid=$(cat "$pid_file")
    local name
    name=$(jq -r ".tunnels.\"$tunnel_name\".name" "$CONFIG_FILE")

    log_info "正在停止隧道: $name (PID: $pid)..."
    kill "$pid"
    local kill_exit_code=$?

    if [ $kill_exit_code -eq 0 ]; then
        rm "$pid_file"
        log_info "✓ 隧道 '$name' 已停止."
    else
        log_error "停止隧道 '$name' 失败. 可能需要手动 'kill $pid'."
    fi
}

# ==============================================================================
#  显示所有隧道的状态 (修正版本)
# ==============================================================================
show_status() {
    echo -e "\n${BLUE}--- SSH 隧道状态 ---${NC}"
    printf "%-15s %-20s %-15s %-20s %s\n" "标识符" "描述" "状态" "本地端口" "目标 (通过SSH主机)"
    echo "------------------------------------------------------------------------------------------------"

    # 使用 while read 循环处理 jq 的输出
    jq -c '.tunnels | to_entries[]' "$CONFIG_FILE" | while read -r entry; do
        local tunnel_name config name local_port remote_host remote_port ssh_host enabled
        tunnel_name=$(echo "$entry" | jq -r '.key')
        config=$(echo "$entry" | jq -r '.value')
        
        name=$(echo "$config" | jq -r '.name')
        local_port=$(echo "$config" | jq -r '.local_port')
        remote_host=$(echo "$config" | jq -r '.remote_host')
        remote_port=$(echo "$config" | jq -r '.remote_port')
        ssh_host=$(echo "$config" | jq -r '.ssh_host')
        enabled=$(echo "$config" | jq -r '.enabled')

        local status_text status_color_code
        if is_tunnel_running "$tunnel_name"; then
            status_text="运行中"
            status_color_code="$GREEN"
        else
            if [ "$enabled" == "true" ]; then
                status_text="已停止"
                status_color_code="$RED"
            else
                status_text="已禁用"
                status_color_code="$YELLOW"
            fi
        fi

        # 【核心修正】将颜色代码放入 printf 格式字符串中
        printf "%-15s %-20s ${status_color_code}%-15s${NC} %-20s %s -> %s:%s\n" \
            "$tunnel_name" \
            "$name" \
            "$status_text" \
            "localhost:$local_port" \
            "$ssh_host" \
            "$remote_host" \
            "$remote_port"
    done
    echo ""
}

# --- Command Handlers ---

handle_start() {
    if [ -n "$1" ]; then
        start_tunnel "$1"
    else
        log_info "启动所有已启用的隧道..."
        jq -r '.tunnels | to_entries[] | select(.value.enabled == true) | .key' "$CONFIG_FILE" | while read -r tunnel_name; do
            start_tunnel "$tunnel_name"
        done
    fi
}

handle_stop() {
    if [ -n "$1" ]; then
        stop_tunnel "$1"
    else
        log_info "停止所有正在运行的隧道..."
        # 确保PID目录存在
        if [ -d "$PID_DIR" ]; then
            ls "$PID_DIR" | sed 's/\.pid$//' | while read -r tunnel_name; do
                stop_tunnel "$tunnel_name"
            done
        fi
    fi
}

handle_restart() {
    log_info "正在重启隧道..."
    handle_stop "$1"
    sleep 1
    handle_start "$1"
}

# --- Main Execution ---
main() {
    check_dependencies

    case "$1" in
        start)
            handle_start "$2"
            ;;
        stop)
            handle_stop "$2"
            ;;
        restart)
            handle_restart "$2"
            ;;
        status|ps)
            show_status
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|ps} [隧道标识符]"
            echo ""
            echo "命令:"
            echo "  start [id]      启动指定隧道, 或所有在 config.json 中启用的隧道."
            echo "  stop [id]       停止指定隧道, 或所有正在运行的隧道."
            echo "  restart [id]    重启指定隧道, 或所有已启用的隧道."
            echo "  status, ps      显示所有定义隧道的状态."
            echo ""
            echo "示例:"
            echo "  $0 start          # 启动所有 'enabled: true' 的隧道"
            echo "  $0 start prod-db    # 只启动 'prod-db' 隧道"
            echo "  $0 status         # 查看当前所有隧道的状态"
            echo "  $0 stop           # 停止所有正在运行的隧道"
            ;;
    esac
}

main "$@"
